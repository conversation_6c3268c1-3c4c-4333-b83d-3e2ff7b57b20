{"level":"error","message":"Error submitting release: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded","name":"TimeoutError","service":"novelupdates-automation","stack":"TimeoutError: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CdpFrame.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async NovelUpdatesBot.submitRelease (C:\\xampp82\\htdocs\\nu\\automation\\novelupdates-bot.js:102:13)\n    at async C:\\xampp82\\htdocs\\nu\\server.js:97:32","timestamp":"2025-06-14 14:34:32"}
{"level":"error","message":"Error submitting release: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded","name":"TimeoutError","service":"novelupdates-automation","stack":"TimeoutError: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CdpFrame.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async NovelUpdatesBot.submitRelease (C:\\xampp82\\htdocs\\nu\\automation\\novelupdates-bot.js:102:13)\n    at async Statement.<anonymous> (C:\\xampp82\\htdocs\\nu\\server.js:153:28)","timestamp":"2025-06-14 14:36:09"}
