2025-06-14 13:32:59 [INFO]: NovelUpdates Automation Server running on port 3000 {"service":"novelupdates-automation"}
2025-06-14 13:32:59 [INFO]: Connected to SQLite database {"service":"novelupdates-automation"}
2025-06-14 13:35:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 13:40:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 13:45:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 13:50:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 13:55:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:00:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:05:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:10:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:15:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:20:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:25:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:30:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:34:03 [INFO]: Browser initialized successfully {"service":"novelupdates-automation"}
2025-06-14 14:34:03 [INFO]: Attempting to login to NovelUpdates {"service":"novelupdates-automation"}
2025-06-14 14:34:18 [INFO]: Successfully logged in to NovelUpdates {"service":"novelupdates-automation"}
2025-06-14 14:34:18 [INFO]: Submitting release for chapter c6 {"service":"novelupdates-automation"}
2025-06-14 14:34:32 [ERROR]: Error submitting release: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded {"service":"novelupdates-automation","name":"TimeoutError","stack":"TimeoutError: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CdpFrame.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async NovelUpdatesBot.submitRelease (C:\\xampp82\\htdocs\\nu\\automation\\novelupdates-bot.js:102:13)\n    at async C:\\xampp82\\htdocs\\nu\\server.js:97:32"}
2025-06-14 14:35:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
2025-06-14 14:35:46 [INFO]: Browser initialized successfully {"service":"novelupdates-automation"}
2025-06-14 14:35:46 [INFO]: Attempting to login to NovelUpdates {"service":"novelupdates-automation"}
2025-06-14 14:35:54 [INFO]: Successfully logged in to NovelUpdates {"service":"novelupdates-automation"}
2025-06-14 14:35:54 [INFO]: Submitting release for chapter c6 {"service":"novelupdates-automation"}
2025-06-14 14:36:09 [ERROR]: Error submitting release: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded {"service":"novelupdates-automation","name":"TimeoutError","stack":"TimeoutError: Waiting for selector `form` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\WaitTask.js:50:34)\n    at IsolatedWorld.waitForFunction (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Realm.js:25:26)\n    at PQueryHandler.waitFor (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\QueryHandler.js:170:95)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CdpFrame.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Frame.js:494:21)\n    at async CdpPage.waitForSelector (C:\\xampp82\\htdocs\\nu\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:1351:20)\n    at async NovelUpdatesBot.submitRelease (C:\\xampp82\\htdocs\\nu\\automation\\novelupdates-bot.js:102:13)\n    at async Statement.<anonymous> (C:\\xampp82\\htdocs\\nu\\server.js:153:28)"}
2025-06-14 14:40:00 [INFO]: Checking for scheduled releases... {"service":"novelupdates-automation"}
